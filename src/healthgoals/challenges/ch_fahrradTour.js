import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import { router, exitWithSlide } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgFahrradTour from "../../../img/healthgoals/hg_fahrradTour.jpg";
import { refreshDragScroll } from "../../../helpers/dragScroll.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
    ? "/healthgoals-overview/hg-fitUmgebung-active"
    : "/healthgoals-overview/hg-fitUmgebung";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget),
      menuId: "challenge-fahrrad-tour-menu"
    })}
  `;
};

/**
 * List Item Content
 */
const trainingsplan = [
  {
    text: "1. Training: 30 Min. Fahrrad fahren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/fahrrad-tour")
  },
  {
    text: "2. Training: 30 Min. Fahrrad fahren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/fahrrad-tour")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const segments = [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext -->
        <p class="challenge-description content-padding">
          Fahrradfahren ist eine gelenkschonende Ausdauersportart, die Dein Herz-Kreislauf-System stärkt und Dir die Möglichkeit gibt, Deine Umgebung auf eine neue Art zu entdecken.
        </p>

        <!-- Grüne Info-Card mit Bullet Points -->
        <div class="standard-container content-padding">
          ${infoCardBullets({
            title: "Was wirst Du erreichen?",
            bulletPoints: [
              "Du verbesserst Deine Ausdauer und Fitness.",
              "Du stärkst Deine Beinmuskulatur.",
              "Du entdeckst neue Wege in Deiner Umgebung.",
              "Du reduzierst Stress und förderst Deine mentale Gesundheit."
            ],
            background: "--info-card-background-green"
          })}
        </div>

        ${sectionTitle("Diese Übungen erwarten Dich")}
        <!-- Image Cards -->
        <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
            ${imageCardNarrow({
              imgPath: imgFahrradTour,
              title: "Fahrrad-Tour",
              altText: "Fahrrad-Tour",
              link: "/training/fahrrad-tour"
            })}
            ${imageCardNarrow({
              imgPath: imgFahrradTour,
              title: "Fahrrad-Tour",
              altText: "Fahrrad-Tour",
              link: "/training/fahrrad-tour"
            })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen -->
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Fahrrad-Übung machen:
            <ul>
              <li>Wenn Du eine Verletzung an Beinen, Rücken oder Handgelenken hast.</li>
              <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
              <li>Wenn Du schwanger bist und Dein Arzt von Radfahren abrät.</li>
            </ul>
            <p><strong>Hinweise:</strong> Trage immer einen Helm. Achte auf verkehrssichere Ausrüstung und beachte die Straßenverkehrsordnung.</p>`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

const segmentedControl = createSegmentedControl(segments);

/**
 * Template für die "Fahrrad-Tour" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templateFahrradTour = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Fahrrad-Tour - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Fahrrad-Tour - Healthgoal aktiv:', appStorage._data.activeHealthGoals.fitUmgebung);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Fahrrad-Tour</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        ${pillsContainer([
          { text: "5 bis 10 km", color: "--pill-green-background", textColor: "--pill-green-text" },
          { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
          { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
        ])}
      </div>

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start Button - nur anzeigen wenn Healthgoal aktiv, Niveau bestimmt und keine andere Challenge aktiv -->
      ${(() => {
        const hasActiveChallenge = appStorage.hasActiveChallenge();
        const isThisChallengeActive = appStorage.isChallengeActive('fahrradTour');
        const canShowButton = appStorage._data.activeHealthGoals.fitUmgebung &&
                             appStorage._data.healthGoalLevel &&
                             (!hasActiveChallenge || isThisChallengeActive);
        return canShowButton;
      })() ? html`
        <div class="standard-container content-padding">
          ${buttonStandard({
            text: "Challenge jetzt starten",
            variant: "primary"
          })}
        </div>
      ` : html`
        <!-- Button ausgeblendet: Bedingungen nicht erfüllt -->
        <div class="standard-container content-padding">
          <p class="caption black-text">
            ${!appStorage._data.activeHealthGoals.fitUmgebung
              ? "Aktiviere zuerst das Gesundheitsziel, um Challenges zu starten."
              : !appStorage._data.healthGoalLevel
                ? "Bestimme zuerst Dein Healthgoal-Niveau, um Challenges zu starten."
                : appStorage.hasActiveChallenge() && !appStorage.isChallengeActive('fahrradTour')
                  ? "Du hast bereits eine andere Challenge gestartet. Schließe diese zuerst ab oder setze sie zurück."
                  : "Challenge kann nicht gestartet werden."
            }
          </p>
        </div>
      `}
    </div>
  `;
};

console.log("Challenge Fahrrad-Tour loaded");
