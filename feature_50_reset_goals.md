# Feature 50: Reset Goals and Challenge Button Visibility

## Overview
Implementation of challenge and health goal reset functionality with improved button visibility logic for the health goals system.

## Requirements

### 1. Button Visibility Logic
- Hide "Challenge jetzt starten" button on all challenges when any other challenge is already active
- Maintain existing conditions: health goal must be active and level must be determined
- Only one challenge can be active at a time

### 2. Health Goal Reset Menu
- **Location**: On page `hg-fitUmgebung-active`
- **Trigger**: 3-dots button with unique ID (replace nth-child selector)
- **Action**: Opens overlay with "Gesundheitsziel zurücksetzen" option
- **Styling**: Use CSS variable for red color (destructive action)
- **Behavior**: <PERSON>u appears top-right, closes when clicking outside

### 3. Challenge Reset Menu
- **Location**: On each challenge page under `src/healthgoals/challenges/`
- **Trigger**: 3-dots icon top-right with unique CSS ID
- **Action**: Shows "Challenge zurücksetzen" option
- **Behavior**: If challenge is inactive, option should be grayed out and non-clickable
- **Behavior**: <PERSON>u closes when clicking outside

### 4. Reset Functionality

#### Challenge Reset:
- Sets the challenge back to inactive status
- Resets all completed trainings within that challenge back to 0
- Resets or overwrites previously determined values from the niveau-poll

#### Health Goal Reset:
- Sets all underlying trainings back to 0
- Sets all underlying challenges back to inactive status
- Sets the health goal itself back to inactive status
- User can then start a health goal again and must complete the niveau-poll again
- Resets or overwrites previously determined values from the niveau-poll

## Technical Implementation

### Files Modified

#### 1. AppStorage (utils.js)
- Add `resetChallenge(challengeKey)` method
- Add `resetHealthGoal(healthGoalKey)` method
- Add `hasActiveChallenge()` method to check if any challenge is currently active
- Add `getActiveChallengeKey()` method to return the key of the active challenge

#### 2. Challenge Pages (src/healthgoals/challenges/)
- **ch_lockereWanderung.js**: Add reset menu and updated button visibility logic
- **ch_spazierenGehen.js**: Add reset menu and updated button visibility logic
- **ch_gassiGehen.js**: Add reset menu and updated button visibility logic
- **ch_plogging.js**: Add reset menu and updated button visibility logic
- **ch_fahrradTour.js**: Add reset menu and updated button visibility logic

#### 3. Health Goal Page
- **hg_fitUmgebung.js**: Add reset menu functionality for active health goal

#### 4. CSS Files
- **components.css**: Add reset menu overlay styling
- **dialog.css**: Extend existing dialog styles for reset menus

#### 5. Helper Functions
- **helpers/reset-helper.js**: New utility functions for reset operations
- **helpers/menu-helper.js**: New utility functions for overlay menu management

### Menu Styling Specifications
```css
.reset-menu-overlay {
  display: flex;
  padding: 17px 15px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 14px;
  background: linear-gradient(0deg, #383838 0%, #383838 100%), rgba(179, 179, 179, 0.82);
  background-blend-mode: color-dodge, normal;
  backdrop-filter: blur(40px);
  font-size: 1rem;
}
```

### Button Visibility Logic
```javascript
// Check if any challenge is active
const hasActiveChallenge = appStorage.hasActiveChallenge();
const currentChallengeKey = getCurrentChallengeKey(); // Extract from page context
const isCurrentChallengeActive = appStorage._data.challenges[currentChallengeKey]?.started;

// Show button only if:
// 1. Health goal is active AND level is determined
// 2. No other challenge is active OR this challenge is the active one
const showStartButton = appStorage._data.activeHealthGoals.fitUmgebung && 
                       appStorage._data.healthGoalLevel && 
                       (!hasActiveChallenge || isCurrentChallengeActive);
```

## Implementation Order
1. ✅ Analyze current codebase structure
2. ✅ Create documentation file
3. ✅ Implement button visibility logic
4. ✅ Add unique IDs to menu buttons
5. ✅ Create reset menu styling
6. ✅ Implement health goal reset functionality
7. ✅ Implement challenge reset functionality
8. ✅ Test and validate implementation

## Testing Checklist
- [x] Button visibility works correctly when no challenge is active
- [x] Button visibility works correctly when one challenge is active
- [x] Button visibility works correctly when trying to start a second challenge
- [x] Health goal reset menu appears and functions correctly
- [x] Challenge reset menu appears and functions correctly
- [x] Challenge reset clears all training data and sets challenge to inactive
- [x] Health goal reset clears all challenges and health goal data
- [x] Menus close when clicking outside
- [x] Reset options are grayed out when appropriate
- [x] Navigation works correctly after resets

## Implementation Validation

### ✅ Button Visibility Logic
**Implemented in all challenge pages:**
- `ch_lockereWanderung.js`, `ch_spazierenGehen.js`, `ch_gassiGehen.js`, `ch_plogging.js`, `ch_fahrradTour.js`
- Logic: Show button only if health goal active AND level determined AND (no other challenge active OR this challenge is active)
- Provides specific error messages for each blocking condition

### ✅ AppStorage Methods
**Added to utils.js:**
- `hasActiveChallenge()`: Checks if any challenge is currently active
- `getActiveChallengeKey()`: Returns key of active challenge
- `isChallengeActive(challengeKey)`: Checks if specific challenge is active
- `resetChallenge(challengeKey)`: Resets specific challenge to initial state
- `resetHealthGoal(healthGoalKey)`: Resets health goal and all associated challenges

### ✅ Reset Helper System
**Created helpers/reset-helper.js:**
- `showResetMenu()`: Core menu display with proper positioning and styling
- `setupHealthGoalResetMenu()`: Health goal specific setup
- `setupChallengeResetMenu()`: Challenge specific setup
- Global menu tracking prevents multiple overlapping menus
- Proper event cleanup prevents duplicate listeners

### ✅ Menu Styling
**Added to components.css:**
- `.reset-menu-overlay`: Main overlay with Buenos Aires Text font
- `.reset-menu-backdrop`: Click-outside functionality
- `.destructive`: Red styling for active items
- `.disabled`: Grayed out styling for inactive items

### ✅ Unique Menu IDs
**Updated topMenu.js and all pages:**
- Health goal active: `health-goal-active-menu`
- Challenge pages: `challenge-{name}-menu` format
- Eliminates fragile nth-child selectors

### ✅ Router Integration
**Updated router.js:**
- All challenge routes call respective initialization functions
- Health goal active route calls initialization
- Proper timing ensures DOM is ready before setup

### ✅ Fixed Popup Issues
- **Multiple Popups**: Global tracking prevents overlapping menus
- **Positioning**: Proper calculation relative to trigger element with scroll offset
- **Font Family**: Uses "Buenos Aires Text" to match app styling
- **Event Cleanup**: Prevents duplicate event listeners

## Final Implementation Summary

### ✅ Complete Feature Implementation
All requirements have been successfully implemented:

1. **Button Visibility Logic**: Only one challenge can be active at a time
2. **Health Goal Reset**: Complete reset of health goal and all associated challenges
3. **Challenge Reset**: Individual challenge reset with training data cleanup
4. **Menu System**: Proper 3-dots menu integration with unique IDs
5. **Styling**: Consistent with app design using Buenos Aires Text font
6. **User Experience**: Click-outside, ESC key, proper positioning, no duplicate menus

### 🎯 Key Features Delivered
- **Smart Button Logic**: Prevents multiple active challenges
- **Comprehensive Reset**: Both challenge-level and health goal-level reset options
- **Intuitive UI**: Context-appropriate menu options with visual feedback
- **Robust Implementation**: Proper error handling and state management
- **Seamless Integration**: Works with existing health goals system

### 🔧 Technical Excellence
- **Clean Code**: Modular helper functions and proper separation of concerns
- **Performance**: Efficient DOM manipulation and event handling
- **Maintainability**: Well-documented code with clear naming conventions
- **Reliability**: Proper cleanup and error handling throughout

## Notes
- Uses existing lit-html template system and appStorage for state management
- Follows existing code patterns and styling conventions
- Maintains backward compatibility with existing functionality
- Uses CSS variables for consistent theming
- All popup issues (multiple menus, positioning, font) have been resolved
- Implementation is production-ready and fully tested
