import { html, render } from "lit-html";
import { appStorage } from "../utils.js";
import { router } from "../router.js";

/**
 * Shows a reset menu overlay for health goals or challenges
 * @param {Object} options - Configuration options
 * @param {string} options.triggerElementId - ID of the element that triggered the menu
 * @param {string} options.type - Type of reset: 'healthGoal' or 'challenge'
 * @param {string} options.key - Key for the health goal or challenge to reset
 * @param {string} options.displayName - Display name for the item being reset
 * @param {boolean} options.isActive - Whether the item is currently active
 * @param {Function} options.onReset - Callback function called after successful reset
 */
export const showResetMenu = ({
  triggerElementId,
  type,
  key,
  displayName,
  isActive = true,
  onReset = () => {}
}) => {
  // Find the trigger element to position the menu
  const triggerElement = document.getElementById(triggerElementId);
  if (!triggerElement) {
    console.error(`Reset menu trigger element with ID '${triggerElementId}' not found`);
    return;
  }

  // Create backdrop element
  const backdrop = document.createElement('div');
  backdrop.className = 'reset-menu-backdrop';

  // Create menu overlay element
  const menuOverlay = document.createElement('div');
  const menuClasses = ['reset-menu-overlay'];
  
  // Add destructive class for active items, disabled for inactive
  if (isActive) {
    menuClasses.push('destructive');
  } else {
    menuClasses.push('disabled');
  }
  
  menuOverlay.className = menuClasses.join(' ');

  // Determine menu text based on type
  const menuText = type === 'healthGoal' 
    ? 'Gesundheitsziel zurücksetzen'
    : 'Challenge zurücksetzen';

  // Set menu content
  menuOverlay.textContent = menuText;

  // Position the menu relative to the trigger element
  const triggerRect = triggerElement.getBoundingClientRect();
  menuOverlay.style.top = `${triggerRect.bottom + 8}px`;
  menuOverlay.style.right = '24px';

  // Function to close the menu
  const closeMenu = () => {
    if (backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    if (menuOverlay.parentNode) {
      menuOverlay.parentNode.removeChild(menuOverlay);
    }
  };

  // Handle menu click
  const handleMenuClick = (event) => {
    event.stopPropagation();
    
    // Don't do anything if disabled
    if (!isActive) {
      return;
    }

    // Perform the reset
    try {
      if (type === 'healthGoal') {
        appStorage.resetHealthGoal(key);
        console.log(`Health goal '${displayName}' reset successfully`);
        
        // Navigate back to health goals overview
        setTimeout(() => {
          router.navigate('/healthgoals-overview');
        }, 100);
      } else if (type === 'challenge') {
        appStorage.resetChallenge(key);
        console.log(`Challenge '${displayName}' reset successfully`);
        
        // Call the onReset callback to refresh the page
        onReset();
      }
    } catch (error) {
      console.error(`Error resetting ${type}:`, error);
    }

    closeMenu();
  };

  // Handle backdrop click (close menu)
  const handleBackdropClick = (event) => {
    event.stopPropagation();
    closeMenu();
  };

  // Add event listeners
  menuOverlay.addEventListener('click', handleMenuClick);
  backdrop.addEventListener('click', handleBackdropClick);

  // Add elements to the page
  const pageContent = document.querySelector('.page-content');
  if (pageContent) {
    pageContent.appendChild(backdrop);
    pageContent.appendChild(menuOverlay);
  } else {
    // Fallback to body if .page-content not found
    document.body.appendChild(backdrop);
    document.body.appendChild(menuOverlay);
  }

  // Close menu when pressing Escape key
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      closeMenu();
      document.removeEventListener('keydown', handleKeyDown);
    }
  };
  document.addEventListener('keydown', handleKeyDown);
};

/**
 * Sets up reset menu functionality for a health goal page
 * @param {string} menuButtonId - ID of the menu button
 * @param {string} healthGoalKey - Key of the health goal
 * @param {string} displayName - Display name of the health goal
 */
export const setupHealthGoalResetMenu = (menuButtonId, healthGoalKey, displayName) => {
  const menuButton = document.getElementById(menuButtonId);
  if (!menuButton) {
    console.error(`Health goal menu button with ID '${menuButtonId}' not found`);
    return;
  }

  // Check if health goal is active
  const isActive = appStorage._data.activeHealthGoals[healthGoalKey];

  menuButton.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();

    showResetMenu({
      triggerElementId: menuButtonId,
      type: 'healthGoal',
      key: healthGoalKey,
      displayName,
      isActive
    });
  });
};

/**
 * Sets up reset menu functionality for a challenge page
 * @param {string} menuButtonId - ID of the menu button
 * @param {string} challengeKey - Key of the challenge
 * @param {string} displayName - Display name of the challenge
 * @param {Function} onReset - Callback function called after reset
 */
export const setupChallengeResetMenu = (menuButtonId, challengeKey, displayName, onReset) => {
  const menuButton = document.getElementById(menuButtonId);
  if (!menuButton) {
    console.error(`Challenge menu button with ID '${menuButtonId}' not found`);
    return;
  }

  // Check if challenge is active (started but not completed)
  const isActive = appStorage.isChallengeActive(challengeKey);

  menuButton.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();

    showResetMenu({
      triggerElementId: menuButtonId,
      type: 'challenge',
      key: challengeKey,
      displayName,
      isActive,
      onReset
    });
  });
};
