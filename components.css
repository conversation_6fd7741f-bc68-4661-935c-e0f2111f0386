/*
 * components.css
 * This file contains shared styles for reusable components
 * These styles are more specific than global styles but are used by multiple components
 */

/* Cards - Base styles for all card types */
.card {
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  border-radius: 12px;
  background: var(--cards-info-card-background-blue);
}

.rounded-img .card img {
  border-radius: 50%;
  overflow: hidden;
}

/* Feature Cards Container */
.feature-cards-small-container {
  display: flex;
  align-items: stretch;
  align-content: flex-start;
  gap: 6px;
  align-self: stretch;
  flex-wrap: wrap;
  flex-direction: row;
  padding: 0 24px 16px;
}

.feature-cards-container {
  display: flex;
  flex-direction: column;
  padding: 0 24px;
  gap: 16px;
}

/* Bottom Navigation - Base styles */
.bottom-menu {
  position: absolute;
  bottom: 40px;
  left: 10px;
  right: 10px;
  width: auto;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: white;
  z-index: 10;
  padding: 0;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 2px 24px 0px rgba(0, 0, 0, 0.08);
}

/* Menu items */
.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex: 1;
  border-top: 2px solid white;
  padding-top: 8px;
}

.menu-item img {
  width: 24px;
  height: 24px;
  filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%)
    hue-rotate(116deg) brightness(90%) contrast(104%);
}

.menu-item span {
  font-size: 10px;
  color: var(--primary-brand);
}

/* Active state */
.menu-item.active {
  color: var(--primary-light-grn);
  border-top: 2px solid var(--bottom-navigation-active, #005E3F);
  padding-top: 8px;
}

/* Buttons - Base styles */
button,
.button-primary {
  all: unset;
  display: flex;
  padding: 12px 16px;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  border-radius: 4px;
  background: var(--button-primary-background-default);
  border: 2px solid var(--button-primary-background-default);
  color: var(--secondary-dark-green);
  font-family: "AOK Buenos Aires Text";
  text-align: center;
  font-size: 1rem;
  font-style: normal;
  font-weight: 600;
  line-height: 1.3rem;
  cursor: pointer;
  box-sizing: border-box;
  margin-top: 1rem;
}

button:hover,
.button-primary:hover {
  border-color: var(--button-primary-background-pressed);
  background-color: var(--button-primary-background-pressed);
}

/* Content Boxes */
.green-bg-box {
  background-color: var(--primary-brand);
}

.blue-bg-box {
  background-color: var(--secondary-blue);
}

.top-bg-box {
  margin-top: -32px;
  padding-bottom: 32px;
  padding-top: 24px;
}

.top-bg-box > h1 {
  margin-top: 0;
}

.training-done-container {
  background-color: var(--primary-brand) !important;
  padding: 16px 0 64px 0 ;
  margin-bottom: -32px ;
  margin-top: 24px;
  gap: 16px;
}

.training-done-container > * {
  margin: 0 ;
}

.training-done-container > p {
  padding: 0;
}

.training-done-container-inactive {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--aok-gray-6) !important;
  padding: 16px 0 64px 0 ;
  margin-bottom: -32px ;
  margin-top: 24px;
  gap: 16px;
}

.training-done-container-inactive > * {
  margin: 0 ;
}

.training-done-container-inactive > p {
  padding: 0;
}

/* Lists */
.list-button {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 16px 0;
  gap: 16px;
}

.list > a:not(:first-child) > .list-button {
  border-top: 1px solid var(--aok-gray-1);
}

.list-button > span {
  display: inline-block;
  line-height: 24px;
  min-height: 24px;
}

/* Poll Container */
.niveau-poll-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  padding-bottom: 0 !important;
}

/* Entfernt das obere Padding für die Features-Seite */
.features-headline {
  margin-top: -32px;
}

/* Reset Menu Overlay Styles */
.reset-menu-overlay {
  position: absolute;
  top: 32px;
  right: 24px;
  z-index: 1000;
  display: flex;
  padding: 17px 15px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 14px;
  background: ;
  background-blend-mode: color-dodge, normal;
  backdrop-filter: blur(40px);
  font-size: 1rem;
  font-family: "Buenos Aires Text", sans-serif;
  color: white;
  cursor: pointer;
  min-width: 200px;
  max-width: 250px;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.reset-menu-overlay:hover {
  background: linear-gradient(0deg, #4a4a4a 0%, #4a4a4a 100%), rgba(179, 179, 179, 0.82);
}

.reset-menu-overlay.destructive {
  color: var(--accent-error, #eb0047);
}

.reset-menu-overlay.disabled {
  color: var(--disabled-grey, #b6bbbe);
  cursor: not-allowed;
  opacity: 0.6;
}

.reset-menu-overlay.disabled:hover {
  background: linear-gradient(0deg, #383838 0%, #383838 100%), rgba(179, 179, 179, 0.82);
}

/* Reset Menu Backdrop */
.reset-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}
